"use client";

 import Header from "@/components/extension/Header";
import Nav from "@/components/extension/Nav";
import Overview from "@/components/extension/Overview";
import { useAuth } from "@/hooks/useAuth";
import { useEnhancedProductData } from "@/hooks/useEnhanceProductData";
import useLocalStorage from "@/hooks/useLocalStorage";
import { ProductData } from "@/lib/types/home";
import { UserCircle, X } from "lucide-react";
import { useState } from "react";

const Home = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [sectionLoading, setSectionLoading] = useState({
    basic: true,
    priceHistory: false,
    offers: false,
    calculator: false,
    gated: false,
  });

  const [sectionErrors, setSectionErrors] = useState({
    basic: null,
    priceHistory: null,
    offers: null,
    calculator: null,
    gated: null,
  });
  const [productInfo, setProductInfo] = useState<ProductData | null>(null);
  const [recentLoginTimestamp, setRecentLoginTimestamp] = useState<
    number | null
  >(null);

  const [selectedCountry, setSelectedCountry] = useLocalStorage<string>(
    "selectedCountry",
    "GB",
  );
  const {
    user,
    isAuthenticated,
    isLoading: authLoading,
    error: authError,
    login,
    logout,
  } = useAuth();
  

  const updateSectionLoading = (section: string, isLoading: boolean) => {
    setSectionLoading((prev) => ({
      ...prev,
      [section]: isLoading,
    }));
  };

  const updateSectionError = (section: string, error: string | null) => {
    setSectionErrors((prev) => ({
      ...prev,
      [section]: error,
    }));
  };

  const {
    productInfo: enhancedProductInfo,
    apiData,
    isLoading: enhancedDataLoading,
    error: enhancedDataError,
    fetchEnhancedProductData,
    refetch,
  } = useEnhancedProductData({
    isAuthenticated,
    selectedCountry,
    updateSectionLoading,
    updateSectionError,
    initialProductInfo : productInfo
  })

  console.log('enhancedProductInfo ===>', enhancedProductInfo);
  

  return (
    <main className="flex flex-col h-screen max-h-screen overflow-hidden bg-gray-100">
      <div className="flex-shrink-0">
        <Nav
          isAuthenticated={isAuthenticated}
          user={user}
          error={authError}
          logout={logout}
          login={login}
          isLoading={authLoading}
          setIsLoading={setIsLoading}
          updateSectionError={(section: string, error: string | null) => {
            setSectionErrors((prev) => ({
              ...prev,
              [section]: error,
            }));
          }}
          fetchEnhancedProductData={async (asin: string) => {
            // Return static mock dat
            return Promise.resolve();
          }}
          setRecentLoginTimestamp={setRecentLoginTimestamp}
          productInfo={productInfo}
        />

        <Header />

        <Overview
          productInfo={productInfo}
          selectedCountry={selectedCountry}
          setSelectedCountry={setSelectedCountry}
          fetchEnhancedProductData={async (asin: string) => {
            // Return static mock dat
            return Promise.resolve();
          }}
        />
      </div>
    </main>
  );
};

export default Home;
