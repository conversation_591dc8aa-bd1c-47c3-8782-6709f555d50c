import { Eye, <PERSON> } from "lucide-react";
import Image from "next/image";

const Header = () => {
  return (
    <div
      className="flex-1 overflow-y-auto p-0"
      style={{ overflowY: "auto", WebkitOverflowScrolling: "touch" }}
    >
      <div className="bg-black">
        <header className="flex items-center justify-between w-full ps-2 py-0">
          <div className="flex-1 flex items-center">
            <h1 className="font-bold text-green-500 text-[1.6rem] tracking-wide">
              CLICKBUYDEALS
            </h1>
          </div>
          <div className="flex-1 flex items-center justify-end">
            <Image
              src="./logo.png"
              alt="ClickBuy Logo"
              height={30}
              width={160}
              className="object-contain"
            />
          </div>
        </header>
      </div>

      <div className="text-white bg-black mx-1 mt-1 px-2 py-1 grid grid-cols-12">
        <div className="col-span-11 text-center">
          <div className="flex items-center justify-center">
            <Eye size={16} className="mr-2" />
            <span className="text-sm font-medium">Watch Training Videos</span>
          </div>
        </div>
        <div className="col-span-1 flex items-center justify-end">
          <div className="border p-[0.5px] rounded-full cursor-pointer">
            <X size={13} color="white" />
          </div>
        </div>
      </div>

      <div className="bg-white flex w-full items-center justify-between text-black px-5 py-2">
        <span className="text-[11px] font-medium">
          Want To Sell More By Cross Selling?
        </span>
        <button
          onClick={() =>
            window.open("https://home.clickbuydeals.com", "_blank")
          }
          className=" text-[11px] bg-black text-white px-1 py-[2px] rounded-sm font-semibold hover:bg-gray-800 transition-colors"
        >
          List On ClickBuyDeals <span className="text-[9px]">🚀</span>
        </button>
      </div>
    </div>
  );
};

export default Header;
