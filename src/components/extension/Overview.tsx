import useLocalStorage from "@/hooks/useLocalStorage";
import { ProductData } from "@/lib/types/home";
import { RefreshCw } from "lucide-react";

const Overview = ({
  productInfo,
  selectedCountry,
  setSelectedCountry,
  fetchEnhancedProductData,
}: {
  productInfo: ProductData | null;
  setSelectedCountry: (country: string) => void;
  selectedCountry: string;
  fetchEnhancedProductData: (asin: string) => Promise<void>;
}) => {
  return (
    <div className="relative mx-1 mt-1 bg-black text-green-500 px-2 py-1 flex items-center justify-center">
      <span className="text-sm font-extrabold tracking-wide">Overview</span>

      <div className="absolute right-2 flex items-center gap-2">
        <select
          value={selectedCountry}
          onChange={(e) => setSelectedCountry(e.target.value)}
          className="bg-gray-800 text-green-500 text-xs rounded px-1 py-0.5 border-none focus:ring-1 focus:ring-green-500"
          title="Select Country"
        >
          <option value="US">US</option>
          <option value="GB">GB</option>
        </select>

        {/* Refresh Button */}
        <button
          onClick={() => {
            if (productInfo?.asin) {
              //     fetchEnhancedProductData(productInfo.asin).catch((err) => {
              //     console.error("Error refreshing data:", err);
              //     updateSectionError(
              //         "basic",
              //         "Could not refresh data. Please try again.",
              //     );
              //     });
            }
          }}
          className="bg-gray-800 hover:bg-gray-700 p-0.5 rounded text-green-500"
          title="Refresh Data"
        >
          <RefreshCw size={14} />
        </button>
      </div>
    </div>
  );
};

export default Overview;
