import useLocalStorage from "@/hooks/useLocalStorage";
import { ProductData, SectionErrorState, SectionLoadingState } from "@/lib/types/home";
import { AlertTriangle, Box, List, RefreshCw, TrendingUp } from "lucide-react";
import Image from "next/image";
import { LoginModal } from "../auth/LoginModal";
import { useState } from "react";
import { LoginCredentials } from "@/lib/auth/types";
import { ProductApiResponse } from "@/lib/api/productApi";

const Overview = ({
  productInfo,
  selectedCountry,
  setSelectedCountry,
  fetchEnhancedProductData,
  updateSectionError,
  isAuthenticated,
  setIsLoading,
  setRecentLoginTimestamp,
  login,
  isLoading,
  error,
  sectionLoading,
  sectionErrors,
  apiData
}: {
  productInfo: ProductData | null;
  setSelectedCountry: (country: string) => void;
  selectedCountry: string;
  fetchEnhancedProductData: (asin: string) => Promise<void>;
  updateSectionError: (section: string, error: string | null) => void;
  isAuthenticated: boolean;
  setIsLoading: (isLoading: boolean) => void;
  setRecentLoginTimestamp: (timestamp: number) => void;
  login: (
      credentials: LoginCredentials,
  ) => Promise<{ success: boolean; error?: string }>;
  isLoading: boolean;
  error: string | null;
  sectionLoading: SectionLoadingState;
  sectionErrors: SectionErrorState;
  apiData: ProductApiResponse | null;
}) => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const handleLogin = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      setRecentLoginTimestamp(Date.now());
      const result = await login({ username, password });

      if (result.success) {
        setIsLoginModalOpen(false);

        if (productInfo && productInfo.asin) {
          if (productInfo && productInfo.asin) {
            fetchEnhancedProductData(productInfo.asin).catch((err) => {
              console.error("Error fetching data after login:", err);
              updateSectionError(
                "basic",
                "Could not refresh data after login. Please try again.",
              );
            });
          }
        }
      } else {
        console.log("Login failed:", result.error || "Unknown error");
      }

      return result;
    } catch (err) {
      console.error("Unexpected error during login:", err);
      return {
        success: false,
        error: "An unexpected error occurred during login. Please try again.",
      };
    }
  };

  // Debug logging
  console.log('Overview component - productInfo:', productInfo);
  console.log('Overview component - sectionLoading:', sectionLoading);
  console.log('Overview component - apiData:', apiData);

  return (
    <div className="mx-1 mt-1 border border-black overflow-hidden bg-white shadow">
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onLogin={handleLogin}
        isLoading={isLoading}
        error={error}
      />
      <div className="relative bg-black text-green-500 px-2 py-1 flex items-center justify-center">
        <span className="text-sm font-extrabold tracking-wide">Overview</span>

        <div className="absolute right-2 flex items-center gap-2">
          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="bg-gray-800 text-green-500 text-xs rounded px-1 py-0.5 border-none focus:ring-1 focus:ring-green-500"
            title="Select Country"
          >
            <option value="US">US</option>
            <option value="GB">GB</option>
          </select>

          {/* Refresh Button */}
          <button
            onClick={() => {
              if (productInfo?.asin) {
                    fetchEnhancedProductData(productInfo.asin).catch((err) => {
                    console.error("Error refreshing data:", err);
                    updateSectionError(
                        "basic",
                        "Could not refresh data. Please try again.",
                    );
                    });
              }
            }}
            className="bg-gray-800 hover:bg-gray-700 p-0.5 rounded text-green-500"
            title="Refresh Data"
          >
            <RefreshCw size={14} />
          </button>
        </div>
      </div>

      <div className=" overflow-hidden bg-white shadow">
        {!isAuthenticated && (
          <div className="bg-yellow-50 p-1 border border-yellow-200 rounded flex items-center space-x-1">
            <AlertTriangle size={12} className="text-yellow-500" />
            <div className="text-xs text-yellow-700 flex-1">
              Login for enhanced data
            </div>
            <button
              onClick={() => setIsLoginModalOpen(true)}
              className="bg-black text-white px-1.5 py-0.5 rounded text-xs hover:bg-gray-800"
            >
              Login
            </button>
          </div>
        )}

      
      <div className="p-2">
        {sectionLoading.basic ? (
          <div className="flex justify-center items-center h-24">
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-500 border-t-transparent"></div>
            <span className="ml-2 text-xs text-gray-600">Loading product data...</span>
          </div>
        ) : sectionErrors.basic ? (
          <div className="flex justify-center items-center h-24 text-red-600">
            <AlertTriangle size={16} className="mr-2" />
            <span className="text-xs">{sectionErrors.basic}</span>
          </div>
        ) : !productInfo ? (
          <div className="flex justify-center items-center h-24 text-gray-500">
            <span className="text-xs">No product data available</span>
          </div>
        ) : (
          <>
            <div className="flex gap-2 mb-2">
              <div className="w-1/4 h-fit border p-1 rounded-md">
                <div className="relative aspect-square">
                  <Image
                    src={
                      productInfo?.mainImage || "/api/placeholder/200/200"
                    }
                    alt={productInfo?.title || "Amazon Product"}
                    width={200}
                    height={200}
                    className="object-contain w-full h-full"
                    onError={() => {
                      console.log("Error loading product image");
                    }}
                  />
                </div>
              </div>

              {/* Product info on right */}
              <div className="w-3/4">
                {/* Product title */}
                <div className="whitespace-normal break-words text-xs font-bold pb-1">
                  {productInfo?.title || "Product Title Not Available"}
                </div>

                <div className="border-b border-gray-200 my-1"></div>

                {/* Secondary info */}
                <div className="grid grid-cols-2 gap-x-2 gap-y-0 text-[11px] mb-2">
                  <div>
                    <span className="font-medium">ASIN:</span>{" "}
                    {productInfo?.asin || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">EAN:</span>{" "}
                    {apiData?.summary?.ean?.[0] || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Brand:</span>{" "}
                    {productInfo?.brand || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Rating:</span>{" "}
                    {productInfo?.rating
                      ? Number(productInfo.rating).toFixed(2)
                      : "N/A"}{" "}
                    ({productInfo?.reviewCount || 0})
                  </div>
                  <div>
                    <span className="font-medium">Price:</span>{" "}
                    {productInfo?.price || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Sales:</span>{" "}
                    {productInfo?.estimated_sales || "N/A"}
                  </div>
                </div>

                {/* Buttons for Product Details */}
                <div className="flex flex-wrap gap-1 mt-1 mb-1">
                  <div className="relative inline-block">
                    <button className="group flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs">
                      <Box size={10} className="mr-0.5" />
                      Dimensions
                      <div className="invisible absolute left-0 top-full mt-1 w-[220px] text-wrap rounded bg-gray-800 p-2 text-start opacity-0 shadow-lg transition-opacity duration-300 group-hover:visible group-hover:opacity-100 z-20">
                        <p className="mb-0.5 text-white text-xs">
                          <strong>Dimensions:</strong>
                        </p>
                        <ul className="list-none text-white text-xs">
                          <li>
                            <strong>Height:</strong>{" "}
                            {productInfo?.dimensions?.height?.value ||
                              "-"}{" "}
                            {productInfo?.dimensions?.height?.unit || ""}
                          </li>
                          <li>
                            <strong>Length:</strong>{" "}
                            {productInfo?.dimensions?.length?.value ||
                              "-"}{" "}
                            {productInfo?.dimensions?.length?.unit || ""}
                          </li>
                          <li>
                            <strong>Width:</strong>{" "}
                            {productInfo?.dimensions?.width?.value ||
                              "-"}{" "}
                            {productInfo?.dimensions?.width?.unit || ""}
                          </li>
                          <li>
                            <strong>Weight:</strong>{" "}
                            {productInfo?.dimensions?.weight?.value ||
                              "-"}{" "}
                            {productInfo?.dimensions?.weight?.unit || ""}
                          </li>
                        </ul>
                      </div>
                    </button>
                  </div>

                  <div className="relative inline-block">
                    <div
                      className="flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        // setIsPinned((prev) => !prev);
                      }}
                      role="button"
                      tabIndex={0}
                    >
                      <List size={10} className="mr-0.5" />
                      Features
                    </div>

                    {/* Pinned Tooltip */}
                    {/* {isPinned && (
                      <div
                        className="fixed z-[9999] mt-1 w-[250px] text-wrap rounded bg-gray-800 text-white shadow-lg transition-opacity duration-300"
                        style={{
                          left: "calc(50% - 125px)",
                          transform: "translateY(30px)",
                          pointerEvents: "auto",
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="flex justify-between items-center px-2 pt-2 pb-1 border-b border-gray-700">
                          <div className="text-xs font-semibold">
                            Features:
                          </div>
                          <span
                            className="text-xs cursor-pointer hover:text-gray-300"
                            onClick={() => setIsPinned(false)}
                          >
                            ✕
                          </span>
                        </div>

                        <div className="max-h-[200px] overflow-y-auto px-2 py-1">
                          {productInfo.features &&
                          productInfo.features.length > 0 ? (
                            <ul className="list-disc pl-4 space-y-1">
                              {productInfo.features.map(
                                (feature, index) => (
                                  <li key={index} className="text-xs">
                                    {feature}
                                  </li>
                                ),
                              )}
                            </ul>
                          ) : (
                            <p className="text-xs text-gray-300">
                              No feature information available
                            </p>
                          )}
                        </div>
                      </div>
                    )} */}
                  </div>

                  {/* Links Popover */}
                  <div className="relative inline-block">
                    <button
                      // onClick={() => setIsPinned(!isPinned)}
                      className="flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs"
                    >
                      <Box size={10} className="mr-0.5" />
                      Links
                    </button>
                    {/* {isPinned && (
                      <div className="absolute left-0 top-full mt-1 w-[220px] text-wrap rounded bg-gray-800 p-2 text-start shadow-lg z-20">
                        <div className="flex flex-col gap-1">
                        
                          {apiData?.summary &&
                          (apiData.summary as any)
                            ?.ebay_active_listing_url ? (
                            <a
                              href={
                                (apiData?.summary as any)
                                  ?.ebay_active_listing_url
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <button className="w-full bg-blue-100 text-blue-800 hover:bg-blue-200 px-1.5 py-0.5 rounded text-xs font-medium">
                                eBay Live
                              </button>
                            </a>
                          ) : (
                            <button
                              disabled
                              className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                            >
                              eBay Live
                            </button>
                          )}

                          
                          {apiData?.summary &&
                          (apiData.summary as any)
                            ?.ebay_sold_listing_url ? (
                            <a
                              href={
                                (apiData?.summary as any)
                                  ?.ebay_sold_listing_url
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <button className="w-full bg-green-100 text-green-800 hover:bg-green-200 px-1.5 py-0.5 rounded text-xs font-medium">
                                eBay Sold
                              </button>
                            </a>
                          ) : (
                            <button
                              disabled
                              className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                            >
                              eBay Sold
                            </button>
                          )}

                          
                          {apiData?.summary &&
                          (apiData.summary as any)?.hagglezon_url ? (
                            <a
                              href={
                                (apiData?.summary as any)?.hagglezon_url
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <button className="w-full bg-yellow-100 text-yellow-800 hover:bg-yellow-200 px-1.5 py-0.5 rounded text-xs font-medium">
                                Hagglezon
                              </button>
                            </a>
                          ) : (
                            <button
                              disabled
                              className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                            >
                              Hagglezon
                            </button>
                          )}

                          
                          <a
                            href={`https://globalesearch.com/?searchTerm=${apiData?.summary?.ean?.[0] || productInfo.asin}&lt=1&lt=2&sortBy=price&category=-1&searchInDescription=false&se=0&se=${getGlobalESearchParams()}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <button className="w-full bg-purple-100 text-purple-800 hover:bg-purple-200 px-1.5 py-0.5 rounded text-xs font-medium">
                              Global
                            </button>
                          </a>
                        </div>
                      </div>
                    )} */}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Key metrics grid */}
        <div className="grid grid-cols-5 gap-1 mb-1">
          {/* <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100">
            <div className="font-bold text-[10px]">Buy Price</div>
            <div className="relative w-full mt-2">
              {isAuthenticated ? (
                <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                  <span className=" text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500">
                    {getCurrencySymbol(selectedCountry)}
                  </span>
                  <input
                    type="text"
                    value={inputPurchasePrice}
                    onChange={(e) => {
                      const value = e.target.value.replace(
                        /[^0-9.]/g,
                        "",
                      );
                      const parts = value.split(".");
                      const formattedValue =
                        parts.length > 2
                          ? parts[0] + "." + parts.slice(1).join("")
                          : value;
                      handlePurchasePriceChange(formattedValue);
                    }}
                    className="w-full py-0 text-xs font-bold text-center"
                  />
                  {isCalculating && (
                    <span className="absolute inset-y-0 right-1 flex items-center">
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-black border-t-transparent"></div>
                    </span>
                  )}
                </div>
              ) : (
                <div className="group relative w-full">
                  <button
                    onClick={() => setIsLoginModalOpen(true)}
                    className="w-full py-0.5 text-center border rounded bg-gray-100 flex items-center justify-center"
                  >
                    <Lock size={14} className="text-gray-500" />
                  </button>
                  <div className="invisible absolute top-0 right-0 translate-x-full ml-1 px-2 py-1 text-xs bg-gray-800 text-white rounded opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 z-50 whitespace-nowrap">
                    Login to use calculator
                  </div>
                </div>
              )}
            </div>
          </div> */}

          {/* <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100">
            <div className="font-bold text-[10px]">Sells price</div>
            <div className="relative w-full mt-2">
              {isAuthenticated ? (
                <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                  <span className="text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500">
                    {getCurrencySymbol(selectedCountry)}
                  </span>
                  <input
                    type="text"
                    value={inputBuyBoxPrice}
                    onChange={(e) => {
                      const value = e.target.value.replace(
                        /[^0-9.]/g,
                        "",
                      );
                      const parts = value.split(".");
                      const formattedValue =
                        parts.length > 2
                          ? parts[0] + "." + parts.slice(1).join("")
                          : value;
                      handleBuyBoxPriceChange(formattedValue);
                    }}
                    className="w-full py-0 text-xs font-bold text-center"
                  />
                  {isCalculating && (
                    <span className="absolute inset-y-0 right-1 flex items-center">
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-black border-t-transparent"></div>
                    </span>
                  )}
                </div>
              ) : (
                <div className="group relative w-full">
                  <button
                    onClick={() => setIsLoginModalOpen(true)}
                    className="w-full py-0.5 text-center border rounded bg-gray-100 flex items-center justify-center"
                  >
                    <Lock size={14} className="text-gray-500" />
                  </button>
                  <div className="invisible absolute top-0 right-0 translate-x-full ml-1 px-2 py-1 text-xs bg-gray-800 text-white rounded opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 z-50 whitespace-nowrap">
                    Login to use calculator
                  </div>
                </div>
              )}
            </div>
          </div> */}

          {/* <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
            <div className="font-bold text-[10px]">BSR</div>
            <div className="text-xs font-bold mb-[2px]">
              {formatWithCommas(
                typeof productInfo?.bsr === "string"
                  ? parseFloat(productInfo?.bsr)
                  : productInfo?.bsr,
              ) ||
                formatWithCommas(
                  productInfo?.pricing?.non_vat_pricing?.sales_rank,
                ) ||
                "N/A"}
            </div>
          </div> */}

          {/* ROI */}
          {/* <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
            <div className="font-bold text-[10px] flex items-center">
              <TrendingUp size={10} className="mr-0.5" />
              ROI
            </div>
            <div
              className={`text-xs font-bold mb-[2px] ${(profitData?.roi || 0) > 0 ? "text-green-600" : "text-red-600"}`}
            >
              {typeof profitData?.roi === "number"
                ? profitData.roi.toFixed(1)
                : "0.0"}
              %
            </div>
          </div>

          <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
            <div className="font-bold text-[10px] flex items-center">
              Profit
            </div>
            <div
              className={`text-xs font-bold mb-[2px] ${(profitData?.profit || 0) > 0 ? "text-green-600" : "text-red-600"}`}
            >
              {formatToCurrency(profitData?.profit || 0)}
            </div>
          </div> */}
        </div>
      </div>
      </div>
    </div>
  );
};

export default Overview;
