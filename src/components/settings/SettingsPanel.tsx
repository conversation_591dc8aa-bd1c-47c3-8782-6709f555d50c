"use client";

import React, { useState, useEffect } from "react";
import { ArrowLeft, Save } from "lucide-react";
import { fetchAiWeights, saveAiWeights } from "@/lib/api/productApi";

export default function SettingsPanel({ onClose }: { onClose: () => void }) {
  // UI states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // basic settings
  const [marketplace, setMarketplace] = useState("UK");
  const [taxSetting, setTaxSetting] = useState("US & Canadian Sellers");
  const [salesTax, setSalesTax] = useState("0.00");
  const [vatEnabled, setVatEnabled] = useState(false);
  const [roiMethod, setRoiMethod] = useState("Method 1 (Default)");
  const [prepFee, setPrepFee] = useState("0.05");
  const [bundleFee, setBundleFee] = useState("0.00");
  const [shippingCost, setShippingCost] = useState("15.00");
  const [weightUnit, setWeightUnit] = useState("kg");
  const [fulfillment, setFulfillment] = useState<"MF" | "FBA">("FBA");

  // AI Adjuster thresholds
  const [minROI, setMinROI] = useState("50");
  const [minProfit, setMinProfit] = useState("50");
  const [estSales, setEstSales] = useState("50");
  const [maxBSR, setMaxBSR] = useState("50");
  const [gatedThreshold, setGatedThreshold] = useState("100");
  const [stockBB, setStockBB] = useState("50");
  const [amzInBB, setAmzInBB] = useState("50");

  // AI Adjuster weights
  const [wROI, setWROI] = useState("21");
  const [wProfit, setWProfit] = useState("13");
  const [wSales, setWSales] = useState("21");
  const [wBSR, setWBSR] = useState("3");
  const [wGated, setWGated] = useState("8");
  const [wStock, setWStock] = useState("17");
  const [wAmz, setWAmz] = useState("17");

  // Calculate total weights
  const totalWeights =
    Number(wROI) +
    Number(wProfit) +
    Number(wSales) +
    Number(wBSR) +
    Number(wGated) +
    Number(wStock) +
    Number(wAmz);

  const isValidWeights = totalWeights === 100;

  // load real values from API on mount
  useEffect(() => {
    setLoading(true);
    fetchAiWeights()
      .then((data) => {
        setMinROI(data.minimum_roi.toString());
        setMinProfit(data.minimum_profit.toString());
        setEstSales((data.estimated_sales || 0).toString());
        setMaxBSR(data.maximum_bsr.toString());
        setGatedThreshold(data.gated ? "100" : "0");
        setStockBB(data.stock_bb.toString());
        setAmzInBB(data.amazon_in_bb.toString());
        setWROI(data.weight_roi.toString());
        setWProfit(data.weight_profit.toString());
        setWSales(data.weight_estimated_sales.toString());
        setWBSR(data.weight_bsr.toString());
        setWGated(data.weight_gated.toString());
        setWStock(data.weight_stock_bb.toString());
        setWAmz(data.weight_amazon_in_bb.toString());
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error loading settings:", err);
        setError("Unable to load settings");
        setLoading(false);
      });
  }, []);

  // Handle saving AI weights
  const handleSave = async () => {
    if (!isValidWeights) return;

    try {
      setSaving(true);
      await saveAiWeights({
        minimum_roi: Number(minROI),
        minimum_profit: Number(minProfit),
        estimated_sales: Number(estSales),
        maximum_bsr: Number(maxBSR),
        gated: Number(gatedThreshold) > 0,
        stock_bb: Number(stockBB),
        amazon_in_bb: Number(amzInBB),
        weight_roi: Number(wROI),
        weight_profit: Number(wProfit),
        weight_estimated_sales: Number(wSales),
        weight_bsr: Number(wBSR),
        weight_gated: Number(wGated),
        weight_stock_bb: Number(wStock),
        weight_amazon_in_bb: Number(wAmz),
      });

      setSaving(false);
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 2000);
    } catch (err) {
      console.error("Error saving settings:", err);
      setSaving(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-100 p-2 relative">
      {/* Simple Error Message */}
      {error && (
        <div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] flex items-center justify-center z-10">
          <div className="bg-white p-3 rounded-lg shadow-sm text-center">
            <div className="w-8 h-8 mx-auto mb-1 bg-blue-50 rounded-full flex items-center justify-center">
              <svg
                className="w-5 h-5 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <p className="text-sm">Settings unavailable</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
            >
              Refresh
            </button>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {loading && !error && (
        <div className="absolute inset-0 bg-white/70 backdrop-blur-[1px] flex items-center justify-center z-10">
          <div className="w-8 h-8 border-2 border-t-transparent border-blue-500 rounded-full animate-spin"></div>
        </div>
      )}

      {/* Header with Save Button */}
      <div className="flex items-center justify-between mb-2 px-2 py-1 bg-blue-50 rounded">
        <div className="flex items-center">
          <button
            onClick={onClose}
            title="Back"
            className="p-1 rounded hover:bg-blue-100"
          >
            <ArrowLeft size={18} className="text-blue-800" />
          </button>
          <h1 className="ml-2 text-base font-semibold text-blue-800">
            Settings
          </h1>
        </div>

        <button
          onClick={handleSave}
          disabled={saving || !isValidWeights}
          className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
            !isValidWeights
              ? "bg-gray-200 text-gray-500 cursor-not-allowed"
              : saving
                ? "bg-blue-400 text-white"
                : "bg-blue-500 text-white hover:bg-blue-600"
          }`}
        >
          <Save size={14} />
          <span>{saving ? "Saving..." : "Save"}</span>
        </button>
      </div>

      {/* Save Success Message */}
      {saveSuccess && (
        <div className="mb-2 px-2 py-1 bg-green-50 border border-green-100 text-green-700 text-xs rounded">
          Settings saved successfully
        </div>
      )}

      <div className="space-y-2 overflow-auto text-sm">
        {/* Fulfillment row */}
        {/* <div className="flex items-center justify-between">
          <span className="text-xs font-medium">Fulfillment</span>
          <div className="flex space-x-4 text-xs">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="fulfill"
                checked={fulfillment === 'MF'}
                onChange={() => setFulfillment('MF')}
                className="mr-1"
              />
              MF
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="fulfill"
                checked={fulfillment === 'FBA'}
                onChange={() => setFulfillment('FBA')}
                className="mr-1"
              />
              FBA
            </label>
          </div> */}
        {/* </div> */}

        {/* AI Weight Adjuster */}
        <div className="bg-white p-2 rounded shadow-sm">
          <div className="flex justify-between items-center mb-1">
            <h2 className="text-xs font-semibold text-gray-700">
              AI Weight Adjuster
            </h2>
            <div
              className={`text-xs ${isValidWeights ? "text-green-600" : "text-amber-600"}`}
            >
              Total: {totalWeights}%
            </div>
          </div>

          <div className="flex space-x-4 text-xs">
            {/* Left: Thresholds */}
            <div className="flex-1 space-y-1">
              <label>Minimum ROI</label>
              <input
                type="number"
                value={minROI}
                onChange={(e) => setMinROI(e.target.value)}
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Minimum Profit</label>
              <input
                type="number"
                value={minProfit}
                onChange={(e) => setMinProfit(e.target.value)}
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Estimated Sales</label>
              <input
                type="number"
                value={estSales}
                onChange={(e) => setEstSales(e.target.value)}
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Maximum BSR</label>
              <input
                type="number"
                value={maxBSR}
                onChange={(e) => setMaxBSR(e.target.value)}
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Gated Penalty</label>
              <input
                type="number"
                value={gatedThreshold}
                onChange={(e) => setGatedThreshold(e.target.value)}
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Stock in Buy Box</label>
              <input
                type="number"
                value={stockBB}
                onChange={(e) => setStockBB(e.target.value)}
                className="w-full rounded-md border px-1 py-0.5"
              />
              <label>Amazon-in-BB Bonus</label>
              <input
                type="number"
                value={amzInBB}
                onChange={(e) => setAmzInBB(e.target.value)}
                className="w-full rounded-md border px-1 py-0.5"
              />
            </div>

            {/* Right: Weights */}
            <div className="flex-1 space-y-1">
              <label>ROI Weight</label>
              <input
                type="number"
                value={wROI}
                onChange={(e) => setWROI(e.target.value)}
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Profit Weight</label>
              <input
                type="number"
                value={wProfit}
                onChange={(e) => setWProfit(e.target.value)}
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Sales Weight</label>
              <input
                type="number"
                value={wSales}
                onChange={(e) => setWSales(e.target.value)}
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>BSR Weight</label>
              <input
                type="number"
                value={wBSR}
                onChange={(e) => setWBSR(e.target.value)}
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Gated Weight</label>
              <input
                type="number"
                value={wGated}
                onChange={(e) => setWGated(e.target.value)}
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Stock BB Weight</label>
              <input
                type="number"
                value={wStock}
                onChange={(e) => setWStock(e.target.value)}
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
              <label>Amazon-in-BB Weight</label>
              <input
                type="number"
                value={wAmz}
                onChange={(e) => setWAmz(e.target.value)}
                className={`w-full rounded-md border px-1 py-0.5 ${!isValidWeights ? "border-amber-300" : ""}`}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
