import React from "react";
import { ExternalLink, Package, Eye, Zap, ArrowRight } from "lucide-react";
import Image from "next/image";

interface ClickbuyAdsProps {
  className?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

const ClickbuyAds: React.FC<ClickbuyAdsProps> = ({
  className = "",
  collapsed = false,
  onToggleCollapse,
}) => {
  const handleStartSelling = () => {
    window.open(
      "https://home.clickbuydeals.com/",
      "_blank",
      "noopener,noreferrer",
    );
  };

  return (
    <div
      className={`border mx-1 border-black overflow-hidden bg-white shadow ${className}`}
    >
      {/* Header - exactly like other components */}
      <div className="bg-black text-green-500 px-2 py-1  flex items-center justify-center">
        <span className="text-sm font-bold">CLICKBUY DEALS</span>
      </div>
      {/* <div className="flex justify-between items-center border-b bg-orange-50 px-2 py-1">
        <h2 className="font-bold text-base text-orange-800 flex items-center">
          <div className="relative mr-2">
            <Image 
              src="https://www.madrona.com/wp-content/uploads/2024/05/AI-Agent-infrastructure-blog-post-ChatGPT.webp" 
              alt="ClickBuy Deals Logo" 
              width={20} 
              height={20} 
              className="rounded object-contain"
            />
          </div>
          ClickBuy Deals
        </h2>
        <div className="flex items-center gap-1">
          <button 
            onClick={onToggleCollapse}
            className="bg-orange-100 hover:bg-orange-200 p-0.5 rounded text-orange-800" 
            title="Toggle Ads"
          >
            <Package size={14} />
          </button>
        </div>
      </div> */}

      {/* Content - only show when not collapsed */}
      {!collapsed && (
        <div className="p-2">
          {/* Headline */}
          <div className="text-center mb-2">
            <h3 className="text-sm font-bold text-gray-800 mb-1">
              Sell With Confidence. Pay Only When You Profit.
            </h3>
            <p className="text-xs text-gray-600 leading-relaxed">
              At ClickBuyDeals, you only pay when you make a sale. No upfront
              listing fees. No hidden costs. No risk.
            </p>
          </div>

          {/* Features */}
          <div className="space-y-1 mb-2">
            <div className="flex items-center space-x-2 p-1 bg-green-50 rounded text-xs">
              <Package size={12} className="text-green-600 flex-shrink-0" />
              <span className="font-medium text-gray-800">
                List your Amazon stock in minutes
              </span>
            </div>

            <div className="flex items-center space-x-2 p-1 bg-blue-50 rounded text-xs">
              <Eye size={12} className="text-blue-600 flex-shrink-0" />
              <span className="font-medium text-gray-800">
                Get instant exposure to millions of UK buyers
              </span>
            </div>

            <div className="flex items-center space-x-2 p-1 bg-purple-50 rounded text-xs">
              <Zap size={12} className="text-purple-600 flex-shrink-0" />
              <span className="font-medium text-gray-800">
                Fast. Simple. Profitable.
              </span>
            </div>
          </div>

          {/* Promise statement */}
          {/* <div className="bg-yellow-50 border border-yellow-200 rounded p-1.5 text-center mb-2">
            <p className="text-xs font-semibold text-yellow-800">
              If you don't sell, you don't pay. That's our promise.
            </p>
          </div> */}

          <div className="bg-black mb-2 text-green-500 px-2 py-2  flex items-center justify-center rounded-md">
            <span className="text-xs ">
              If you don't sell, you don't pay. That's our promise.
            </span>
          </div>

          {/* Call to action */}
          <div className="text-center space-y-1">
            <p className="text-xs mb-2 text-gray-700 flex items-center justify-center">
              <ArrowRight size={12} className="mr-1 text-blue-600" />
              Start selling today - it takes less than 5 minutes
            </p>

            <button
              onClick={handleStartSelling}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-2 px-4 rounded text-xs hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-1 shadow"
            >
              <span>Start Selling Today</span>
              <ExternalLink size={12} />
            </button>
          </div>

          {/* Disclaimer */}
          <div className="text-center mt-1">
            <p className="text-xs text-gray-500">
              Join thousands of sellers already profiting with ClickBuyDeals
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClickbuyAds;
