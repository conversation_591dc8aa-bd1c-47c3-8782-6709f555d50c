import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts";
import {
  ValueType,
  NameType,
  Props as TooltipProps,
} from "recharts/types/component/DefaultTooltipContent";

// Add currency symbol helper function
const getCurrencySymbol = (country: string): string => {
  switch (country) {
    case "US":
      return "$";
    case "UK":
    case "GB":
      return "£";
    case "DE":
    case "FR":
    case "IT":
    case "ES":
      return "€";
    case "JP":
    case "CN":
      return "¥";
    case "CA":
      return "C$";
    case "IN":
      return "₹";
    case "MX":
      return "$";
    default:
      return "£"; // Default to pound since GB is the default country
  }
};

// Define types
interface BuyBoxChartProps {
  buyBoxHistory?: BuyBoxHistoryData;
  country?: string; // Add country prop
}

interface BuyBoxHistoryData {
  "30_days"?: Record<string, number>;
  "90_days"?: Record<string, number>;
  "180_days"?: Record<string, number>;
  "365_days"?: Record<string, number>;
}

interface ChartDataItem {
  id: string;
  value: number;
  color: string;
}

// Define color palette
const COLORS = [
  "#0f172a", // dark blue/black
  "#2a667f", // teal
  "#3f8a8c", // seafoam
  "#a0d9d9", // light teal
  "#f8edcb", // cream
  "#f4c95d", // yellow
  "#e87e31", // orange
  "#bd5b34", // rust
  "#a02c2c", // dark red
  "#d97706", // amber
  "#65a30d", // lime
  "#0284c7", // sky blue
];

const BuyBoxChart: React.FC<BuyBoxChartProps> = ({
  buyBoxHistory,
  country = "GB", // Default to GB if not provided
}) => {
  const [timeFilter, setTimeFilter] = useState<string>("180_days");
  const [chartData, setChartData] = useState<ChartDataItem[]>([]);
  const [totalDays, setTotalDays] = useState<number>(0);
  const [noDataForPeriod, setNoDataForPeriod] = useState<boolean>(false);

  // Get currency symbol based on country
  const currencySymbol = getCurrencySymbol(country);

  // Format currency with dynamic currency symbol
  const formatCurrency = (value: number | undefined | null): string => {
    if (value === undefined || value === null) return `${currencySymbol}0.00`;
    return `${currencySymbol}${value.toFixed(2)}`;
  };

  useEffect(() => {
    if (buyBoxHistory && buyBoxHistory[timeFilter as keyof BuyBoxHistoryData]) {
      // Type assertion to handle the optional property
      const periodData = buyBoxHistory[
        timeFilter as keyof BuyBoxHistoryData
      ] as Record<string, number> | undefined;

      // Check if we have any data for this period
      if (!periodData || Object.keys(periodData).length === 0) {
        setNoDataForPeriod(true);
        setChartData([]);
        setTotalDays(0);
        return;
      }

      setNoDataForPeriod(false);
      const data: ChartDataItem[] = Object.entries(periodData).map(
        ([id, count], index) => {
          return {
            id: id,
            value: count,
            color: COLORS[index % COLORS.length],
          };
        },
      );

      // Sort by value in descending order
      data.sort((a, b) => b.value - a.value);

      // Calculate total for percentages
      const total = data.reduce((sum, item) => sum + item.value, 0);
      setTotalDays(total);

      setChartData(data);
    } else {
      setNoDataForPeriod(true);
      setChartData([]);
      setTotalDays(0);
    }
  }, [buyBoxHistory, timeFilter]);

  // TypeScript types for renderCustomizedLabel props
  interface LabelProps {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
    index: number;
  }

  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
  }: LabelProps) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos((-midAngle * Math.PI) / 180);
    const y = cy + radius * Math.sin((-midAngle * Math.PI) / 180);

    return percent > 0.05 ? (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor="middle"
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    ) : null;
  };

  // Fixed custom tooltip using the proper Recharts types
  interface CustomTooltipProps extends TooltipProps<ValueType, NameType> {
    active?: boolean;
    payload?: any[];
  }

  const customTooltip = (props: CustomTooltipProps) => {
    const { active, payload } = props;

    if (active && payload && payload.length) {
      const data = payload[0].payload as ChartDataItem;
      return (
        <div className="bg-white p-2 border rounded shadow-sm text-xs">
          <p className="font-bold">{data.id}</p>
          <p>
            {data.value} days ({((data.value / totalDays) * 100).toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  const renderLegend = () => {
    // Only show top 6 sellers in legend to avoid clutter
    const legendData = chartData.slice(0, 6);

    return (
      <div className="text-xs mt-2">
        <div className="font-medium mb-1">Top Sellers in Buy Box:</div>
        <div className="grid grid-cols-2 gap-x-2 gap-y-1">
          {legendData.map((entry, index) => (
            <div key={`legend-${index}`} className="flex items-center">
              <div
                className="w-3 h-3 mr-1 rounded-sm"
                style={{ backgroundColor: entry.color }}
              />
              <span className="truncate">{entry.id}</span>
              <span className="ml-1 text-gray-600">
                ({((entry.value / totalDays) * 100).toFixed(0)}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Define time period options
  const timePeriods: string[] = ["30_days", "90_days", "180_days", "365_days"];

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-2">
        <div className="text-sm font-medium">Buy Box History</div>
        <div className="flex space-x-1">
          {timePeriods.map((period) => (
            <button
              key={period}
              onClick={() => setTimeFilter(period)}
              className={`text-xs px-1.5 py-0.5 rounded ${
                timeFilter === period
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 hover:bg-gray-200"
              }`}
            >
              {period.replace("_", " ")}
            </button>
          ))}
        </div>
      </div>

      <div className="w-full" style={{ height: 200 }}>
        {noDataForPeriod ? (
          <div className="h-full flex items-center justify-center bg-gray-50 rounded">
            <p className="text-gray-500 text-xs">
              No Buy Box data available for this period
            </p>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={customTooltip} />
            </PieChart>
          </ResponsiveContainer>
        )}
      </div>

      {!noDataForPeriod && renderLegend()}
    </div>
  );
};

export default BuyBoxChart;
