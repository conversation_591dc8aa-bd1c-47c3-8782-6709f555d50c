import { ProductData } from "@/lib/types/home";
import { useState, useCallback } from "react";
import {
  fetchProductData,
  mapApiResponseToProductData,
  extractAsinFromUrl,
  parseAmazonProductPage,
  fetchProfitCalculator,
  fetchGatedStatus,
  ProductApiResponse,
} from "@/lib/api/productApi";

interface ProductWarnings {
  amz_in_buy_box: boolean;
  adult_product: boolean;
  meltable: boolean;
  variations?: any;
}

interface NonVatPricing {
  buy_box_price: number;
  fba_fee: number;
  profit: number;
  referral_fee: number;
  roi: number;
  sales_rank: number;
  seller_price: number;
  total_fee: number;
  variable_closing_fee: number;
}

interface ProductPricing {
  non_vat_pricing: NonVatPricing;
}

interface SectionLoadingState {
  [key: string]: boolean;
}

interface SectionErrorState {
  [key: string]: string | null;
}

interface UseEnhancedProductDataProps {
  isAuthenticated: boolean;
  selectedCountry: string;
  updateSectionLoading: (section: string, loading: boolean) => void;
  updateSectionError: (section: string, error: string | null) => void;
  initialProductInfo?: ProductData | null;
}

interface UseEnhancedProductDataReturn {
  productInfo: ProductData | null;
  apiData: ProductApiResponse | null;
  isLoading: boolean;
  error: string | null;
  dataLoadingProgress: number;
  fetchEnhancedProductData: (asin: string) => Promise<void>;
  refetch: (asin?: string) => Promise<void>;
  clearError: () => void;
  resetData: () => void;
  setProductInfo: React.Dispatch<React.SetStateAction<ProductData | null>>;
  setDataLoadingProgress: React.Dispatch<React.SetStateAction<number>>;
}

export const useEnhancedProductData = ({
  isAuthenticated,
  selectedCountry,
  updateSectionLoading,
  updateSectionError,
  initialProductInfo = null,
}: UseEnhancedProductDataProps): UseEnhancedProductDataReturn => {
  const [productInfo, setProductInfo] = useState<ProductData | null>(
    initialProductInfo,
  );
  const [apiData, setApiData] = useState<ProductApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dataLoadingProgress, setDataLoadingProgress] = useState(0);
  const [lastFetchedAsin, setLastFetchedAsin] = useState<string | null>(null);

  const fetchEnhancedProductData = useCallback(
    async (asin: string) => {
      try {
        setIsLoading(true);
        updateSectionLoading("basic", true);
        setLastFetchedAsin(asin);
        console.log(
          `Starting enhanced data fetch for ASIN: ${asin}, country: ${selectedCountry}`,
        );

        // Fetch data from API
        let apiResponse;
        try {
          apiResponse = await fetchProductData(asin, selectedCountry);
        } catch (err) {
          console.error("Error in API call:", err);

          if (isAuthenticated) {
            setError(
              "Failed to fetch detailed product information. Please try again.",
            );
          }
          setIsLoading(false);
          updateSectionLoading("basic", false);
          return;
        }

        // Store raw API data
        setApiData(apiResponse);

        // Map API data to the format our UI expects
        const enhancedData = mapApiResponseToProductData(apiResponse);

        // Merge with existing data
        setProductInfo((prevState) => {
          // Safely extract monthly_sold from the API
          const apiMonthlySold = apiResponse?.summary?.monthly_sold;
          const fallbackEstimatedSales = prevState?.estimated_sales || 0;

          // Use API monthly_sold if it's a valid number > 0, otherwise fall back
          const resolvedEstimatedSales =
            typeof apiMonthlySold === "number" && apiMonthlySold > 0
              ? apiMonthlySold
              : fallbackEstimatedSales;

          const mergedWarnings: ProductWarnings = {
            amz_in_buy_box: false,
            adult_product: false,
            meltable: false,
            ...(prevState?.warnings || {}),
            ...(enhancedData.warnings || {}),
            ...(enhancedData.warnings?.variations
              ? { variations: enhancedData.warnings.variations }
              : {}),
          };

          const defaultNonVatPricing: NonVatPricing = {
            buy_box_price: 0,
            fba_fee: 0,
            profit: 0,
            referral_fee: 0,
            roi: 0,
            sales_rank: 0,
            seller_price: 0,
            total_fee: 0,
            variable_closing_fee: 0,
          };

          const mergedPricing: ProductPricing = {
            non_vat_pricing: {
              ...defaultNonVatPricing,
              ...(prevState?.pricing?.non_vat_pricing || {}),
              ...(enhancedData.pricing?.non_vat_pricing || {}),
            },
          };

          const mergedData: ProductData = {
            ...enhancedData,
            estimated_sales: resolvedEstimatedSales,
            mainImage: prevState?.mainImage || enhancedData.mainImage,
            warnings: mergedWarnings,
            offers: enhancedData.offers || prevState?.offers,
            features: enhancedData.features || prevState?.features,
            pricing: mergedPricing,
            ...(prevState?.graph_data
              ? { graph_data: prevState.graph_data }
              : {}),
          };

          console.log(
            "Final merged product data with resolved estimated_sales:",
            mergedData,
          );
          return mergedData;
        });

        setIsLoading(false);
        updateSectionLoading("basic", false);
        updateSectionError("basic", null);
      } catch (err) {
        console.error("Error fetching enhanced product data:", err);

        if (isAuthenticated) {
          setError(
            "Failed to fetch detailed product information. Please try again.",
          );
        }
        setIsLoading(false);
        updateSectionLoading("basic", false);
      }
    },
    [
      isAuthenticated,
      selectedCountry,
      fetchProductData,
      mapApiResponseToProductData,
      updateSectionLoading,
      updateSectionError,
    ],
  );

  // Refetch function - can refetch with last ASIN or provide new one
  const refetch = useCallback(
    async (asin?: string) => {
      const asinToFetch = asin || lastFetchedAsin;
      if (!asinToFetch) {
        console.warn(
          "No ASIN provided for refetch and no previous ASIN stored",
        );
        return;
      }
      await fetchEnhancedProductData(asinToFetch);
    },
    [fetchEnhancedProductData, lastFetchedAsin],
  );

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Reset all data function
  const resetData = useCallback(() => {
    setProductInfo(null);
    setApiData(null);
    setIsLoading(false);
    setError(null);
    setDataLoadingProgress(0);
    setLastFetchedAsin(null);
  }, []);

  return {
    // Data
    productInfo,
    apiData,

    // Loading states
    isLoading,
    dataLoadingProgress,

    // Error state
    error,

    // Functions
    fetchEnhancedProductData,
    refetch,
    clearError,
    resetData,
    setProductInfo,
    setDataLoadingProgress,
  };
};
